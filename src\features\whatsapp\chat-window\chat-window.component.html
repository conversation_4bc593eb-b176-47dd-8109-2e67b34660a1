<div class="chat-container">
  <div class="chat-header">Chat with Support</div>

  <div class="chat-body">
    <div *ngFor="let msg of messages" [ngClass]="msg.type === 'sent' ? 'message sent' : 'message received'">
      {{ msg.text }}
    </div>
  </div>

  <div class="chat-input">
    <input [(ngModel)]="message" type="text" placeholder="Type a message..." />
    <button (click)="sendMessage()">Send</button>
  </div>
</div>
