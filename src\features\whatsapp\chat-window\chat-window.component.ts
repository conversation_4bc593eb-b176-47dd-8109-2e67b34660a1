import { NgClass } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-chat-window',
  standalone: true,
  imports: [NgClass, FormsModule],
  templateUrl: './chat-window.component.html',
  styleUrl: './chat-window.component.scss'
})
export class ChatWindowComponent {

  message = '';
  messages: { text: string, type: 'sent' | 'received' }[] = [
    { text: 'Hi, how can I help you?', type: 'received' },
    { text: 'I need support with my order.', type: 'sent' }
  ];

  sendMessage() {
    if (this.message.trim()) {
      this.messages.push({ text: this.message, type: 'sent' });
      this.message = '';
    }
  }

}
